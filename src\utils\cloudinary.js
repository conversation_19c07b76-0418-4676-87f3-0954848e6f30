import {v2 as cloudinary} from "cloudinary";
import fs from "fs";

cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET
});


const uploadOnCloudinary = async (localFilePath) => {
    try {
        if (!localFilePath) return null;
        const response = await cloudinary.uploader.upload(localFilePath, {
            resource_type: "auto",
        });
        // remove local file after successful upload
        try { fs.unlinkSync(localFilePath); } catch {}
        return response; // must return so callers can read response.url
    } catch (error) {
        // remove local file if upload failed
        try { fs.unlinkSync(localFilePath); } catch {}
        return null;
    }
}

export { uploadOnCloudinary };
